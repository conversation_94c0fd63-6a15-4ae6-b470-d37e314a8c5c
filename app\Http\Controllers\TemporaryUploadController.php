<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class TemporaryUploadController extends Controller
{
    /**
     * Handle temporary file upload
     */
    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|image|max:10240', // Max 10MB
        ]);
        
        try {
            $file = $request->file('file');
            
            // Generate unique temporary filename
            $tempFilename = 'temp_' . Str::random(40) . '.' . $file->getClientOriginalExtension();
            
            // Simpan ke livewire-tmp directory
            $path = $file->storeAs('livewire-tmp', $tempFilename, 'public');
            
            if ($path) {
                return response()->json([
                    'success' => true,
                    'tempPath' => $path,
                    'tempUrl' => Storage::disk('public')->url($path),
                    'originalName' => $file->getClientOriginalName(),
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('Temporary upload error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Gagal upload file temporary',
            ], 500);
        }
        
        return response()->json([
            'success' => false,
            'error' => 'Upload gagal',
        ], 500);
    }
} 