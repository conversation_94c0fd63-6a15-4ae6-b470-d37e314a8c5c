{{-- Raja<PERSON>icker with Temporary Upload Support --}}

@include('rajapicker::components.forms.raja-picker')

@pushOnce('scripts')
<script>
document.addEventListener('alpine:init', () => {
    // Override rajaPicker untuk mendukung temporary upload
    const originalRajaPicker = Alpine.data('rajaPicker');
    
    Alpine.data('rajaPicker', (config) => ({
        ...originalRajaPicker(config),
        
        // Override handleFileUpload untuk upload ke temporary directory
        async handleFileUpload(event) {
            // Tampilkan indikator loading pada tombol Upload
            this.isUploading = true;
            const files = Array.from(event.target.files);
            if (files.length === 0) return;

            // Validate files
            for (const file of files) {
                if (!this.validateFile(file)) {
                    return;
                }
            }

            // Upload files ke temporary directory
            try {
                const formData = new FormData();

                if (this.isMultiple) {
                    files.forEach((file, index) => {
                        formData.append(`files[${index}]`, file);
                    });
                } else {
                    formData.append('file', files[0]);
                }

                formData.append('collection', this.collection);
                formData.append('convertWebp', this.convertWebp.toString());
                formData.append('_token', document.querySelector('meta[name="csrf-token"]')
                    .getAttribute('content'));

                // Upload ke temporary endpoint
                const response = await fetch('/api/temporary-upload', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    
                    // Update state dengan path temporary
                    if (this.isMultiple) {
                        if (!Array.isArray(this.value)) {
                            this.value = [];
                        }
                        this.value.push(result.tempPath);
                        
                        // Add to preview list
                        this.selectedMediaList.push({
                            id: 'temp_' + Date.now(),
                            name: result.originalName,
                            url: result.tempPath,
                            file_name: result.originalName,
                            size: 0,
                            isTemporary: true
                        });
                    } else {
                        this.value = result.tempPath;
                        this.selectedMedia = {
                            id: 'temp_' + Date.now(),
                            name: result.originalName,
                            url: result.tempPath,
                            file_name: result.originalName,
                            size: 0,
                            isTemporary: true
                        };
                    }

                    // Force sync with Livewire immediately
                    this.syncWithLivewire(this.value);
                    this.showNotification('File diupload ke temporary. Akan disimpan permanent setelah form disubmit.', 'info');
                } else {
                    const error = await response.json();
                    this.showNotification(error.error || 'Gagal mengupload gambar', 'error');
                }
            } catch (error) {
                console.error('Upload error:', error);
                this.showNotification('Terjadi kesalahan saat mengupload', 'error');
            }

            // Reset file input
            event.target.value = '';
            // Sembunyikan indikator loading tombol Upload
            this.isUploading = false;
        },
        
        // Override formatFileSize untuk handle temporary files
        formatFileSize(bytes) {
            if (!bytes || bytes === 0) return 'Temporary';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        // Override addStoragePrefix untuk handle temporary paths
        addStoragePrefix(url) {
            if (!url || typeof url !== 'string') return url;
            
            // Jika sudah ada /storage/ atau livewire-tmp, return as is
            if (url.includes('/storage/') || url.includes('livewire-tmp')) {
                return url;
            }

            // Don't add prefix if URL already has it or is a full URL
            if (url.startsWith('/storage/') || url.startsWith('http')) {
                return url;
            }

            // Add /storage/ prefix - handle both with and without leading slash
            if (url.startsWith('/')) {
                return '/storage' + url;
            }

            return '/storage/' + url;
        }
    }));
});
</script>

@push('styles')
<style>
/* Style untuk temporary files */
.raja-picker-preview img[src*="livewire-tmp"] {
    border: 2px dashed #f59e0b !important;
    position: relative;
}

.raja-picker-preview .single-preview:has(img[src*="livewire-tmp"])::after,
.raja-picker-preview .relative:has(img[src*="livewire-tmp"])::after {
    content: 'TEMPORARY';
    position: absolute;
    top: -8px;
    right: -8px;
    background: #f59e0b;
    color: white;
    padding: 2px 8px;
    font-size: 10px;
    border-radius: 4px;
    font-weight: bold;
}
</style>
@endpush
@endPushOnce 