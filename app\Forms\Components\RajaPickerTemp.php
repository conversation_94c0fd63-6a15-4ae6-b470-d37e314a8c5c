<?php

namespace App\Forms\Components;

use Mo<PERSON><PERSON>\Rajapicker\Filament\Forms\Components\RajaPicker;
use Mo<PERSON>les\Rajapicker\Models\Media;
use Mo<PERSON>les\Rajapicker\Models\RajaGaleri;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

/**
 * RajaPickerTemp - Custom RajaPicker dengan temporary upload support
 * 
 * Komponen ini menyimpan file di direktori temporary (/storage/livewire-tmp)
 * sebelum form disubmit. Setelah form disubmit, file akan dipindahkan
 * ke direktori permanent.
 */
class RajaPickerTemp extends RajaPicker
{
    // Flag untuk mengidentifikasi temporary files
    protected bool $useTemporaryUpload = true;
    
    // Override view untuk custom display
    protected string $view = 'forms.components.raja-picker-temp';
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Override dehydration untuk handle pemindahan file temporary
        $this->dehydrateStateUsing(function ($state) {
            return $this->processTemporaryFiles($state);
        });
    }
    
    /**
     * Process temporary files saat form submit
     */
    protected function processTemporaryFiles($state)
    {
        if (!$state) {
            return $state;
        }
        
        // Handle single file
        if (is_string($state) && str_contains($state, 'livewire-tmp')) {
            return $this->moveTemporaryFile($state);
        }
        
        // Handle multiple files
        if (is_array($state)) {
            $processedFiles = [];
            foreach ($state as $file) {
                if (is_string($file) && str_contains($file, 'livewire-tmp')) {
                    $permanentUrl = $this->moveTemporaryFile($file);
                    if ($permanentUrl) {
                        $processedFiles[] = $permanentUrl;
                    }
                } else {
                    $processedFiles[] = $file;
                }
            }
            return $processedFiles;
        }
        
        return $state;
    }
    
    /**
     * Move temporary file to permanent storage
     */
    protected function moveTemporaryFile(string $temporaryUrl): ?string
    {
        try {
            // Extract path dari URL temporary
            $tempPath = str_replace('/storage/', '', $temporaryUrl);
            
            // Cek apakah file temporary masih ada
            if (!Storage::disk('public')->exists($tempPath)) {
                Log::warning('Temporary file not found: ' . $tempPath);
                return null;
            }
            
            // Get file info
            $originalName = basename($tempPath);
            $extension = pathinfo($originalName, PATHINFO_EXTENSION);
            
            // Generate permanent filename
            $permanentFilename = $this->generatePermanentFileName($originalName, $extension);
            
            // Tentukan directory permanent
            $permanentDir = 'uploads/' . ($this->getDirectory() ?: $this->getCollection());
            $permanentPath = $permanentDir . '/' . $permanentFilename;
            
            // Create directory if not exists
            Storage::disk('public')->makeDirectory($permanentDir);
            
            // Move file
            if (Storage::disk('public')->move($tempPath, $permanentPath)) {
                // Create media record
                $this->createMediaRecord($permanentPath, $originalName);
                
                // Return clean URL (without /storage/ prefix)
                return $permanentPath;
            }
            
        } catch (\Exception $e) {
            Log::error('Error moving temporary file: ' . $e->getMessage());
        }
        
        return null;
    }
    
    /**
     * Generate permanent filename
     */
    protected function generatePermanentFileName(string $originalName, string $extension): string
    {
        $nameSlug = Str::slug(pathinfo($originalName, PATHINFO_FILENAME));
        $timestamp = time();
        $random = Str::lower(Str::random(6));
        
        return "{$nameSlug}-{$timestamp}-{$random}.{$extension}";
    }
    
    /**
     * Create media record in database
     */
    protected function createMediaRecord(string $path, string $originalName): void
    {
        try {
            // Create RajaGaleri instance
            $rajaGaleri = new RajaGaleri();
            $rajaGaleri->save();
            
            // Get file info
            $fullPath = Storage::disk('public')->path($path);
            $mimeType = mime_content_type($fullPath);
            $size = filesize($fullPath);
            
            // Get image dimensions if applicable
            $dimensions = [];
            if (str_starts_with($mimeType, 'image/')) {
                list($width, $height) = getimagesize($fullPath);
                $dimensions = ['width' => $width, 'height' => $height];
            }
            
            // Create media record
            $media = new Media();
            $media->model_type = get_class($rajaGaleri);
            $media->model_id = $rajaGaleri->id;
            $media->uuid = Str::uuid();
            $media->collection_name = $this->getCollection();
            $media->name = pathinfo($originalName, PATHINFO_FILENAME);
            $media->file_name = basename($path);
            $media->mime_type = $mimeType;
            $media->disk = 'public';
            $media->conversions_disk = 'public';
            $media->size = $size;
            $media->manipulations = [];
            $media->custom_properties = $dimensions;
            $media->generated_conversions = [];
            $media->responsive_images = [];
            
            // Set user_id if authenticated
            if (Auth::check()) {
                $media->user_id = Auth::id();
            }
            
            $media->save();
            
            // Generate thumbnails
            if (class_exists(\Modules\Rajapicker\Services\RajaPickerThumbnailService::class)) {
                $thumbnailService = new \Modules\Rajapicker\Services\RajaPickerThumbnailService();
                $thumbnailService->generateAllThumbnails($path);
            }
            
        } catch (\Exception $e) {
            Log::error('Error creating media record: ' . $e->getMessage());
        }
    }
    
    /**
     * Get custom upload API endpoint
     */
    public function getUploadApiEndpoint(): string
    {
        return '/api/temporary-upload';
    }
} 